/**
 * @file AnalyticsCacheManager Comprehensive Test Suite
 * @filepath server/src/platform/tracking/core-data/__tests__/AnalyticsCacheManager.test.ts
 * @task-id T-TSK-01.SUB-01.1.IMP-08
 * @component AnalyticsCacheManager
 * @compliance OA Framework Testing Standards, MEM-SAFE-002, Anti-Simplification Policy
 * @created 2025-09-02
 * 
 * @description
 * Comprehensive test suite for AnalyticsCacheManager providing complete functionality testing
 * including memory-safe patterns, resilient timing integration, cache operations, error handling,
 * performance metrics, and enterprise-grade quality validation.
 * 
 * @coverage-target ≥95% test coverage with complete business logic validation
 * @anti-simplification Complete functionality testing - no shortcuts or feature reduction
 * @memory-safety MEM-SAFE-002 compliance validation with BaseTrackingService inheritance
 * @resilient-timing P1 dual-field pattern testing (_resilientTimer, _metricsCollector)
 */

import { AnalyticsCacheManager } from '../AnalyticsCacheManager';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';
import {
  TAnalyticsData,
  TAnalyticsQuery,
  TAnalyticsResult,
  TAnalyticsCacheEntry,
  TAnalyticsCacheMetrics,
  TAnalyticsCacheStrategy,
  TAnalyticsCacheHealth,
  TCacheMetrics,
  TTrackingConfig
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST CONFIGURATION AND SETUP
// ============================================================================

const TEST_CONFIG: Partial<TTrackingConfig> = {
  testMode: true,
  logging: {
    level: 'error',
    format: 'json',
    rotation: false,
    maxFileSize: 10
  }
};

const PERFORMANCE_THRESHOLDS = {
  CACHE_OPERATION_MAX_MS: 10,
  RETRIEVAL_MAX_MS: 5,
  INITIALIZATION_MAX_MS: 100,
  SHUTDOWN_MAX_MS: 50
};

// ============================================================================
// MOCK DATA FACTORIES
// ============================================================================

/**
 * Create mock analytics query
 */
function createMockAnalyticsQuery(overrides?: Partial<TAnalyticsQuery>): TAnalyticsQuery {
  return {
    type: 'performance',
    parameters: {
      metric: 'response_time',
      aggregation: 'average'
    },
    filters: {
      timeRange: {
        start: new Date(Date.now() - 3600000),
        end: new Date()
      }
    },
    timeRange: {
      start: new Date(Date.now() - 3600000),
      end: new Date()
    },
    cacheable: true,
    ttl: 300000, // 5 minutes
    ...overrides
  };
}

/**
 * Create mock analytics result
 */
function createMockAnalyticsResult(overrides?: Partial<TAnalyticsResult>): TAnalyticsResult {
  return {
    queryId: `query-${Date.now()}`,
    query: createMockAnalyticsQuery(),
    data: { values: [1, 2, 3, 4, 5] },
    results: [{ metric: 'response_time', value: 150 }],
    metadata: {
      executionTime: 25,
      dataPoints: 100,
      accuracy: 0.95,
      timestamp: new Date(),
      source: 'test-analytics',
      recordCount: 100,
      cached: false
    },
    performance: {
      cacheHit: false,
      processingTime: 25,
      memoryUsed: 1024,
      optimizationApplied: true
    },
    ...overrides
  };
}

/**
 * Create mock analytics data
 */
function createMockAnalyticsData(overrides?: Partial<TAnalyticsData>): TAnalyticsData {
  const query = createMockAnalyticsQuery();
  const result = createMockAnalyticsResult();
  
  return {
    queryKey: `key-${Date.now()}`,
    query,
    result,
    timestamp: Date.now(),
    lastAccessed: new Date(),
    accessCount: 0,
    size: JSON.stringify(result).length,
    compressed: false,
    ...overrides
  };
}

// ============================================================================
// TIMER COORDINATOR MOCKING
// ============================================================================

describe('AnalyticsCacheManager - Comprehensive Test Suite', () => {
  const coordinator: any = getTimerCoordinator();
  let manager: AnalyticsCacheManager;

  beforeEach(() => {
    // Mock timer coordinator for memory-safe testing
    jest.spyOn(coordinator as any, 'clearServiceTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'clearAllTimers').mockImplementation(() => {});
    jest.spyOn(coordinator as any, 'createCoordinatedInterval').mockImplementation(
      (...args: any[]) => `${args[2]}:${args[3]}`
    );

    // Create fresh manager instance for each test
    manager = new AnalyticsCacheManager(TEST_CONFIG);
  });

  afterEach(async () => {
    // Ensure proper cleanup after each test
    if (manager) {
      try {
        await manager.shutdown();
      } catch (error) {
        // Ignore shutdown errors in tests
      }
    }
    jest.clearAllMocks();
  });

  // ============================================================================
  // INITIALIZATION AND LIFECYCLE TESTS
  // ============================================================================

  describe('Initialization and Lifecycle', () => {
    test('should initialize successfully with default configuration', async () => {
      const startTime = Date.now();
      
      await manager.initialize();
      
      const initTime = Date.now() - startTime;
      expect(initTime).toBeLessThan(PERFORMANCE_THRESHOLDS.INITIALIZATION_MAX_MS);
      expect(manager.isHealthy()).toBe(true);
    });

    test('should initialize with custom configuration', async () => {
      const customConfig: Partial<TTrackingConfig> = {
        ...TEST_CONFIG,
        analytics: {
          cacheSize: 500,
          cacheTTL: 1800000 // 30 minutes
        }
      };

      const customManager = new AnalyticsCacheManager(customConfig);
      await customManager.initialize();
      
      expect(customManager.isHealthy()).toBe(true);
      
      await customManager.shutdown();
    });

    test('should shutdown gracefully with proper cleanup', async () => {
      await manager.initialize();
      
      const startTime = Date.now();
      await manager.shutdown();
      const shutdownTime = Date.now() - startTime;
      
      expect(shutdownTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SHUTDOWN_MAX_MS);
      
      // Verify timer coordinator cleanup was called
      const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
      if (svcSpy && svcSpy.mock) {
        expect(svcSpy).toHaveBeenCalledWith('AnalyticsCacheManager');
      } else {
        expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
      }
    });

    test('should handle multiple initialization calls gracefully', async () => {
      await manager.initialize();
      await manager.initialize(); // Second call should not cause issues
      
      expect(manager.isHealthy()).toBe(true);
    });
  });

  // ============================================================================
  // RESILIENT TIMING INTEGRATION TESTS
  // ============================================================================

  describe('Resilient Timing Integration (P1)', () => {
    test('should initialize resilient timing infrastructure', async () => {
      await manager.initialize();
      
      // Verify dual-field pattern exists
      const resilientTimer = (manager as any)._resilientTimer;
      const metricsCollector = (manager as any)._metricsCollector;
      
      expect(resilientTimer).toBeDefined();
      expect(metricsCollector).toBeDefined();
      expect(typeof resilientTimer.start).toBe('function');
      expect(typeof metricsCollector.recordTiming).toBe('function');
    });

    test('should record timing metrics for cache operations', async () => {
      await manager.initialize();
      
      const mockData = createMockAnalyticsData();
      await manager.cacheAnalyticsData('test-key', mockData);
      
      const metricsCollector = (manager as any)._metricsCollector;
      const metrics = metricsCollector.createCompatibleMetrics();
      
      expect(Object.keys(metrics)).toEqual(
        expect.arrayContaining(['cacheAnalyticsData'])
      );
    });

    test('should handle timing infrastructure failures gracefully', async () => {
      // Mock timing infrastructure to throw errors
      const originalInit = (manager as any)._initializeResilientTimingSync;
      (manager as any)._initializeResilientTimingSync = jest.fn(() => {
        throw new Error('Timing infrastructure failure');
      });
      
      await manager.initialize();
      
      // Should still function with fallback timing
      const mockData = createMockAnalyticsData();
      const result = await manager.cacheAnalyticsData('test-key', mockData);
      
      expect(result).toBe(true);
      
      // Restore original method
      (manager as any)._initializeResilientTimingSync = originalInit;
    });
  });

  // ============================================================================
  // CACHE OPERATIONS TESTS
  // ============================================================================

  describe('Cache Operations', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should cache analytics data successfully', async () => {
      const mockData = createMockAnalyticsData();
      const key = 'test-cache-key';
      
      const startTime = Date.now();
      const result = await manager.cacheAnalyticsData(key, mockData);
      const cacheTime = Date.now() - startTime;
      
      expect(result).toBe(true);
      expect(cacheTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION_MAX_MS);
    });

    test('should retrieve cached analytics data', async () => {
      const mockData = createMockAnalyticsData();
      const key = 'test-retrieval-key';
      
      // Cache the data first
      await manager.cacheAnalyticsData(key, mockData);
      
      // Retrieve the data
      const startTime = Date.now();
      const retrievedData = await manager.getCachedAnalytics(key);
      const retrievalTime = Date.now() - startTime;
      
      expect(retrievedData).not.toBeNull();
      expect(retrievedData?.queryKey).toBe(mockData.queryKey);
      expect(retrievalTime).toBeLessThan(PERFORMANCE_THRESHOLDS.RETRIEVAL_MAX_MS);
    });

    test('should return null for non-existent cache keys', async () => {
      const result = await manager.getCachedAnalytics('non-existent-key');
      expect(result).toBeNull();
    });

    test('should handle cache with different strategies', async () => {
      const mockData = createMockAnalyticsData();
      const strategies = ['default', 'analytics-query'];
      
      for (const strategy of strategies) {
        const key = `test-${strategy}-key`;
        const result = await manager.cacheAnalyticsData(key, mockData, {
          strategy,
          ttl: 600000 // 10 minutes
        });
        
        expect(result).toBe(true);
        
        const retrievedData = await manager.getCachedAnalytics(key);
        expect(retrievedData).not.toBeNull();
      }
    });
  });

  // ============================================================================
  // CACHE COMPRESSION AND OPTIMIZATION TESTS
  // ============================================================================

  describe('Cache Compression and Optimization', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should handle compressed cache entries', async () => {
      const mockData = createMockAnalyticsData();
      const key = 'compressed-test-key';

      const result = await manager.cacheAnalyticsData(key, mockData, {
        compression: true,
        tier: 'primary'
      });

      expect(result).toBe(true);

      const retrievedData = await manager.getCachedAnalytics(key);
      expect(retrievedData).not.toBeNull();
      expect(retrievedData?.compressed).toBe(true);
    });

    test('should optimize cache performance', async () => {
      // Add multiple cache entries
      for (let i = 0; i < 10; i++) {
        const mockData = createMockAnalyticsData();
        await manager.cacheAnalyticsData(`key-${i}`, mockData);
      }

      // Perform optimization
      await manager.optimizeCache();

      // Verify cache is still functional
      const testData = createMockAnalyticsData();
      const result = await manager.cacheAnalyticsData('post-optimization-key', testData);
      expect(result).toBe(true);
    });

    test('should clear cache completely', async () => {
      // Add test data
      const mockData = createMockAnalyticsData();
      await manager.cacheAnalyticsData('clear-test-key', mockData);

      // Verify data exists
      let retrievedData = await manager.getCachedAnalytics('clear-test-key');
      expect(retrievedData).not.toBeNull();

      // Clear cache
      await manager.clearCache();

      // Verify data is gone
      retrievedData = await manager.getCachedAnalytics('clear-test-key');
      expect(retrievedData).toBeNull();
    });
  });

  // ============================================================================
  // ANALYTICS QUERY EXECUTION TESTS
  // ============================================================================

  describe('Analytics Query Execution', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should execute analytics query successfully', async () => {
      const mockQuery = createMockAnalyticsQuery({
        type: 'performance',
        parameters: { metric: 'response_time' }
      });

      const result = await manager.executeQuery(mockQuery);

      expect(result).toBeDefined();
      expect(result.queryId).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.performance).toBeDefined();
    });

    test('should cache query results when cacheable', async () => {
      const mockQuery = createMockAnalyticsQuery({
        cacheable: true,
        ttl: 300000
      });

      // Execute query (should cache result)
      const result1 = await manager.executeQuery(mockQuery);

      // Execute same query again (should hit cache)
      const result2 = await manager.executeQuery(mockQuery);

      expect(result1.queryId).toBe(result2.queryId);
      expect(result2.performance.cacheHit).toBe(true);
    });

    test('should handle non-cacheable queries', async () => {
      const mockQuery = createMockAnalyticsQuery({
        cacheable: false
      });

      const result = await manager.executeQuery(mockQuery);

      expect(result).toBeDefined();
      expect(result.performance.cacheHit).toBe(false);
    });

    test('should get cached result by query key', async () => {
      const mockQuery = createMockAnalyticsQuery();
      const mockResult = createMockAnalyticsResult();
      const queryKey = 'test-query-key';

      // Cache result
      await manager.cacheResult(queryKey, mockResult, mockQuery);

      // Retrieve cached result
      const cachedResult = await manager.getCachedResult(queryKey);

      expect(cachedResult).not.toBeNull();
      expect(cachedResult?.queryId).toBe(mockResult.queryId);
    });
  });

  // ============================================================================
  // CACHE METRICS AND PERFORMANCE TESTS
  // ============================================================================

  describe('Cache Metrics and Performance', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should provide basic cache metrics', async () => {
      const metrics = manager.getCacheMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.hits).toBe('number');
      expect(typeof metrics.misses).toBe('number');
      expect(typeof metrics.hitRatio).toBe('number');
      expect(typeof metrics.cacheSize).toBe('number');
      expect(typeof metrics.memoryUsage).toBe('number');
      expect(metrics.lastCleanup).toBeInstanceOf(Date);
    });

    test('should provide detailed cache metrics', async () => {
      // Add some cache operations to generate metrics
      const mockData = createMockAnalyticsData();
      await manager.cacheAnalyticsData('metrics-test-key', mockData);
      await manager.getCachedAnalytics('metrics-test-key'); // Hit
      await manager.getCachedAnalytics('non-existent-key'); // Miss

      const detailedMetrics = await manager.getDetailedCacheMetrics();

      expect(detailedMetrics).toBeDefined();
      expect(detailedMetrics.totalEntries).toBeGreaterThanOrEqual(0);
      expect(detailedMetrics.hitRate).toBeGreaterThanOrEqual(0);
      expect(detailedMetrics.missRate).toBeGreaterThanOrEqual(0);
      expect(detailedMetrics.healthStatus).toBeDefined();
      expect(detailedMetrics.lastOptimization).toBeInstanceOf(Date);

      if (detailedMetrics.realTime) {
        expect(typeof detailedMetrics.realTime.hitRate).toBe('number');
        expect(typeof detailedMetrics.realTime.performanceScore).toBe('number');
      }
    });

    test('should track cache hit and miss rates', async () => {
      const mockData = createMockAnalyticsData();

      // Cache some data
      await manager.cacheAnalyticsData('hit-test-key', mockData);

      // Generate hits and misses
      await manager.getCachedAnalytics('hit-test-key'); // Hit
      await manager.getCachedAnalytics('hit-test-key'); // Hit
      await manager.getCachedAnalytics('miss-test-key'); // Miss

      const metrics = await manager.getDetailedCacheMetrics();

      expect(metrics.totalHits).toBeGreaterThan(0);
      expect(metrics.totalMisses).toBeGreaterThan(0);
      expect(metrics.hitRate).toBeGreaterThan(0);
      expect(metrics.missRate).toBeGreaterThan(0);
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should handle invalid cache data gracefully', async () => {
      const invalidData = null as any;

      const result = await manager.cacheAnalyticsData('invalid-key', invalidData);

      expect(result).toBe(false);
    });

    test('should handle cache operations before initialization', async () => {
      const uninitializedManager = new AnalyticsCacheManager(TEST_CONFIG);
      const mockData = createMockAnalyticsData();

      // Should handle gracefully without throwing
      const result = await uninitializedManager.cacheAnalyticsData('test-key', mockData);
      expect(result).toBe(false);

      await uninitializedManager.shutdown();
    });

    test('should handle expired cache entries', async () => {
      const mockData = createMockAnalyticsData();
      const key = 'expiry-test-key';

      // Cache with very short TTL
      await manager.cacheAnalyticsData(key, mockData, {
        ttl: 1 // 1ms TTL
      });

      // Wait for expiry
      await new Promise(resolve => setTimeout(resolve, 10));

      // Should return null for expired entry
      const result = await manager.getCachedAnalytics(key);
      expect(result).toBeNull();
    });

    test('should handle cache size limits and eviction', async () => {
      // Fill cache beyond typical limits
      const promises = [];
      for (let i = 0; i < 100; i++) {
        const mockData = createMockAnalyticsData();
        promises.push(manager.cacheAnalyticsData(`bulk-key-${i}`, mockData));
      }

      await Promise.all(promises);

      // Cache should still be functional
      const testData = createMockAnalyticsData();
      const result = await manager.cacheAnalyticsData('post-bulk-key', testData);
      expect(result).toBe(true);
    });

    test('should handle concurrent cache operations', async () => {
      const concurrentOps = [];

      // Perform multiple concurrent operations
      for (let i = 0; i < 20; i++) {
        const mockData = createMockAnalyticsData();
        concurrentOps.push(manager.cacheAnalyticsData(`concurrent-${i}`, mockData));
      }

      const results = await Promise.all(concurrentOps);

      // All operations should succeed
      results.forEach(result => {
        expect(result).toBe(true);
      });
    });
  });

  // ============================================================================
  // CACHE HEALTH AND MONITORING TESTS
  // ============================================================================

  describe('Cache Health and Monitoring', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should assess cache health status', async () => {
      const health = await manager.getDetailedCacheMetrics();

      expect(health.healthStatus).toBeDefined();
      expect(['healthy', 'degraded', 'critical', 'offline', 'unhealthy']).toContain(health.healthStatus);
    });

    test('should detect performance issues', async () => {
      // Simulate poor cache performance by creating many misses
      for (let i = 0; i < 50; i++) {
        await manager.getCachedAnalytics(`miss-key-${i}`);
      }

      const metrics = await manager.getDetailedCacheMetrics();

      expect(metrics.totalMisses).toBeGreaterThan(0);
      expect(metrics.missRate).toBeGreaterThan(0);
    });

    test('should provide cache recommendations', async () => {
      // Force low hit rate scenario
      for (let i = 0; i < 20; i++) {
        await manager.getCachedAnalytics(`recommendation-miss-${i}`);
      }

      const metrics = await manager.getDetailedCacheMetrics();

      // Health assessment should provide recommendations for poor performance
      expect(metrics).toBeDefined();
    });
  });

  // ============================================================================
  // MEMORY SAFETY AND COMPLIANCE TESTS
  // ============================================================================

  describe('Memory Safety and MEM-SAFE-002 Compliance', () => {
    test('should extend BaseTrackingService correctly', () => {
      expect(manager).toBeInstanceOf(require('../base/BaseTrackingService').BaseTrackingService);
    });

    test('should use memory-safe interval creation', async () => {
      await manager.initialize();

      // Verify that createSafeInterval was used (indirectly through timer coordinator)
      const createIntervalSpy = (coordinator as any).createCoordinatedInterval as jest.Mock;
      expect(createIntervalSpy).toHaveBeenCalled();
    });

    test('should handle resource cleanup on shutdown', async () => {
      await manager.initialize();

      // Add some cache data
      const mockData = createMockAnalyticsData();
      await manager.cacheAnalyticsData('cleanup-test', mockData);

      // Shutdown should clean up resources
      await manager.shutdown();

      // Verify timer cleanup was called
      const svcSpy = (coordinator as any).clearServiceTimers as jest.Mock;
      if (svcSpy && svcSpy.mock) {
        expect(svcSpy).toHaveBeenCalledWith('AnalyticsCacheManager');
      } else {
        expect((coordinator as any).clearAllTimers).toHaveBeenCalled();
      }
    });

    test('should enforce memory boundaries', async () => {
      await manager.initialize();

      // Test with large data sets to verify memory management
      const largeDataPromises = [];
      for (let i = 0; i < 50; i++) {
        const largeData = createMockAnalyticsData({
          result: {
            ...createMockAnalyticsResult(),
            data: new Array(1000).fill({ value: Math.random() })
          }
        });
        largeDataPromises.push(manager.cacheAnalyticsData(`large-${i}`, largeData));
      }

      await Promise.all(largeDataPromises);

      // Manager should still be healthy after handling large data
      expect(manager.isHealthy()).toBe(true);
    });
  });

  // ============================================================================
  // CACHE STRATEGY AND CONFIGURATION TESTS
  // ============================================================================

  describe('Cache Strategy and Configuration', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should support different cache strategies', async () => {
      const strategies = ['default', 'analytics-query'];
      const mockData = createMockAnalyticsData();

      for (const strategy of strategies) {
        const result = await manager.cacheAnalyticsData(`strategy-${strategy}`, mockData, {
          strategy
        });
        expect(result).toBe(true);
      }
    });

    test('should handle multi-tier caching', async () => {
      const tiers = ['primary', 'secondary', 'tertiary'];
      const mockData = createMockAnalyticsData();

      for (const tier of tiers) {
        const result = await manager.cacheAnalyticsData(`tier-${tier}`, mockData, {
          tier
        });
        expect(result).toBe(true);
      }
    });

    test('should respect TTL configuration', async () => {
      const mockData = createMockAnalyticsData();
      const customTTL = 100; // 100ms

      await manager.cacheAnalyticsData('ttl-test', mockData, {
        ttl: customTTL
      });

      // Should be available immediately
      let result = await manager.getCachedAnalytics('ttl-test');
      expect(result).not.toBeNull();

      // Wait for TTL expiry
      await new Promise(resolve => setTimeout(resolve, customTTL + 50));

      // Should be expired
      result = await manager.getCachedAnalytics('ttl-test');
      expect(result).toBeNull();
    });
  });

  // ============================================================================
  // INTEGRATION AND INTERFACE COMPLIANCE TESTS
  // ============================================================================

  describe('Integration and Interface Compliance', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should implement IAnalytics interface correctly', async () => {
      // Test executeQuery method
      const query = createMockAnalyticsQuery();
      const result = await manager.executeQuery(query);
      expect(result).toBeDefined();

      // Test getCachedResult method
      const cachedResult = await manager.getCachedResult('test-key');
      expect(cachedResult).toBeNull(); // No cached result for this key

      // Test cacheResult method
      const mockResult = createMockAnalyticsResult();
      await manager.cacheResult('interface-test', mockResult, query);

      const retrievedResult = await manager.getCachedResult('interface-test');
      expect(retrievedResult).not.toBeNull();
    });

    test('should implement ICacheableService interface correctly', async () => {
      // Test getCacheMetrics method
      const metrics = manager.getCacheMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.hits).toBe('number');
      expect(typeof metrics.misses).toBe('number');

      // Test clearCache method
      const mockData = createMockAnalyticsData();
      await manager.cacheAnalyticsData('clear-test', mockData);

      await manager.clearCache();

      const result = await manager.getCachedAnalytics('clear-test');
      expect(result).toBeNull();
    });

    test('should provide service health status', () => {
      expect(typeof manager.isHealthy()).toBe('boolean');
    });

    test('should handle service lifecycle correctly', async () => {
      // Test initialization
      await manager.initialize();
      expect(manager.isHealthy()).toBe(true);

      // Test operations
      const mockData = createMockAnalyticsData();
      const result = await manager.cacheAnalyticsData('lifecycle-test', mockData);
      expect(result).toBe(true);

      // Test shutdown
      await manager.shutdown();

      // Operations after shutdown should fail gracefully
      const postShutdownResult = await manager.cacheAnalyticsData('post-shutdown', mockData);
      expect(postShutdownResult).toBe(false);
    });
  });

  // ============================================================================
  // PERFORMANCE AND SCALABILITY TESTS
  // ============================================================================

  describe('Performance and Scalability', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    test('should meet performance thresholds for cache operations', async () => {
      const mockData = createMockAnalyticsData();

      // Test cache write performance
      const writeStart = Date.now();
      await manager.cacheAnalyticsData('perf-write', mockData);
      const writeTime = Date.now() - writeStart;

      expect(writeTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION_MAX_MS);

      // Test cache read performance
      const readStart = Date.now();
      await manager.getCachedAnalytics('perf-write');
      const readTime = Date.now() - readStart;

      expect(readTime).toBeLessThan(PERFORMANCE_THRESHOLDS.RETRIEVAL_MAX_MS);
    });

    test('should handle high-volume operations efficiently', async () => {
      const operationCount = 100;
      const operations = [];

      const startTime = Date.now();

      for (let i = 0; i < operationCount; i++) {
        const mockData = createMockAnalyticsData();
        operations.push(manager.cacheAnalyticsData(`volume-${i}`, mockData));
      }

      await Promise.all(operations);

      const totalTime = Date.now() - startTime;
      const avgTimePerOp = totalTime / operationCount;

      expect(avgTimePerOp).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION_MAX_MS);
    });

    test('should maintain performance under concurrent load', async () => {
      const concurrentUsers = 10;
      const operationsPerUser = 10;

      const userOperations = Array.from({ length: concurrentUsers }, (_, userIndex) =>
        Array.from({ length: operationsPerUser }, async (_, opIndex) => {
          const mockData = createMockAnalyticsData();
          return manager.cacheAnalyticsData(`user-${userIndex}-op-${opIndex}`, mockData);
        })
      );

      const startTime = Date.now();

      const results = await Promise.all(
        userOperations.map(userOps => Promise.all(userOps))
      );

      const totalTime = Date.now() - startTime;

      // All operations should succeed
      results.forEach(userResults => {
        userResults.forEach(result => {
          expect(result).toBe(true);
        });
      });

      // Performance should remain reasonable under load
      const totalOps = concurrentUsers * operationsPerUser;
      const avgTimePerOp = totalTime / totalOps;
      expect(avgTimePerOp).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_OPERATION_MAX_MS * 2); // Allow 2x threshold for concurrent load
    });
  });
});
